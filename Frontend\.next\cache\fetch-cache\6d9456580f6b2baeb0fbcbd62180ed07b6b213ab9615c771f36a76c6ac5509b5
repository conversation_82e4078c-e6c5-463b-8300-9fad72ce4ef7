{"kind": "FETCH", "data": {"headers": {"access-control-allow-credentials": "true", "access-control-allow-origin": "", "connection": "keep-alive", "content-length": "996", "content-security-policy": "script-src 'self' 'unsafe-inline' cdn.marutitech.com;img-src 'self' data: strapi.io cdn.marutitech.com storage.googleapis.com cdn-gcp.new.marutitech.com;media-src cdn.marutitech.com cdn-gcp.new.marutitech.com blob:;connect-src 'self' https:;default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';style-src 'self' https: 'unsafe-inline'", "content-type": "application/json; charset=utf-8", "date": "Thu, 31 Jul 2025 08:37:31 GMT", "referrer-policy": "no-referrer", "server": "nginx/1.24.0", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-powered-by": "Strapi <strapi.io>"}, "body": "eyJkYXRhIjp7ImlkIjoxLCJhdHRyaWJ1dGVzIjp7ImNyZWF0ZWRBdCI6IjIwMjQtMDYtMDVUMTI6MzU6MzUuNTM1WiIsInVwZGF0ZWRBdCI6IjIwMjUtMDctMjVUMTA6NDI6NDEuMTAwWiIsInB1Ymxpc2hlZEF0IjoiMjAyNC0wNi0wNVQxMjozNTozOC4yNjhaIiwic2VvIjp7ImlkIjo1NTIsInRpdGxlIjoiTWFydXRpIFRlY2hsYWJzOiBBSS9NTCB8IEN1c3RvbSBTb2Z0d2FyZSBEZXZlbG9wbWVudCBDb21wYW55IiwiZGVzY3JpcHRpb24iOiJMZXZlcmFnZSBvdXIgY3VzdG9tIHNvZnR3YXJlIGRldmVsb3BtZW50IHNlcnZpY2VzIHRvIGRlc2lnbiBzZWN1cmUsIHNjYWxhYmxlLCByZWxpYWJsZSwgYW5kIGZ1dHVyaXN0aWMgc29sdXRpb25zIHRoYXQgZm9zdGVyIHVucGFyYWxsZWxlZCBidXNpbmVzcyBncm93dGguIiwidHlwZSI6IndlYnNpdGUiLCJ1cmwiOiJodHRwczovL21hcnV0aXRlY2guY29tLyIsInNpdGVfbmFtZSI6Ik1hcnV0aSBUZWNobGFicyIsImxvY2FsZSI6ImVuLVVTIiwic2NoZW1hIjpudWxsLCJrZXl3b3JkcyI6eyJpZCI6MTA5LCJrZXl3b3JkIjoiQUksIE1MLCBzb2Z0d2FyZSBkZXZlbG9wbWVudCwgY3VzdG9tIHNvZnR3YXJlIHNvbHV0aW9ucywgYXV0b21hdGlvbiwgYnVzaW5lc3MgZ3Jvd3RoIn0sIm1ldGFQcm9wZXJ0aWVzIjpbeyJpZCI6MTA4MywibmFtZSI6Im9nOnRpdGxlIiwiY29udGVudCI6Ik1hcnV0aSBUZWNobGFiczogQUkvTUwgfCBDdXN0b20gU29mdHdhcmUgRGV2ZWxvcG1lbnQgQ29tcGFueSJ9LHsiaWQiOjEwODQsIm5hbWUiOiJvZzpkZXNjcmlwdGlvbiIsImNvbnRlbnQiOiJMZXZlcmFnZSBvdXIgY3VzdG9tIHNvZnR3YXJlIGRldmVsb3BtZW50IHNlcnZpY2VzIHRvIGRlc2lnbiBzZWN1cmUsIHNjYWxhYmxlLCByZWxpYWJsZSwgYW5kIGZ1dHVyaXN0aWMgc29sdXRpb25zIHRoYXQgZm9zdGVyIHVucGFyYWxsZWxlZCBidXNpbmVzcyBncm93dGguIn1dLCJpbWFnZSI6eyJkYXRhIjpudWxsfX19fSwibWV0YSI6e319", "status": 200, "url": "https://dev-content.marutitech.com/api/home-page?populate=seo&populate=seo.image&populate=seo.metaProperties&populate=seo.keywords,seo.schema"}, "revalidate": 31536000, "tags": []}