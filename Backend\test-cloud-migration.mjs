/**
 * @fileoverview Test script for Cloud Migration Cost Calculator form handler
 * This script tests the cloud migration handler with sample data that matches
 * the frontend form structure to ensure proper integration.
 */

import { handler } from './api/cloud-migration/index.mjs';

// Sample test data that matches the frontend form structure
const testFormData = {
  firstName: "<PERSON>",
  lastName: "<PERSON>", 
  emailAddress: "<EMAIL>",
  companyName: "Tech Solutions Inc",
  phoneNumber: "+1234567890",
  city: "New York",
  country: "USA",
  howCanWeHelpYou: "Cloud migration consultation and cost optimization",
  
  // Cost calculation results
  minimum_cost: "50000",
  maximum_migration_cost: "100000", 
  totalCost: "75000",
  
  // Cloud Migration Assessment Questions - Section 1
  which_elements_are_you_planning_to_migrate_to_the_cloud: "Applications and databases",
  approximately_how_many_servers_do_you_intend_to_migrate: "10-50",
  what_is_the_type_of_data_migration_you_intend_to_do: "Full migration with minimal downtime",
  what_is_your_current_it_infrastructure_setup: "On-premises data center",
  what_is_the_total_capacity_of_your_servers: "500GB - 1TB",
  what_is_the_current_monthly_infrastructure_cost_of_your_current_setup: "$10,000 - $25,000",
  
  // Section 2: Workload & Resource Analysis
  what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud: ["Cost reduction", "Scalability", "Disaster recovery"],
  what_type_of_workloads_do_you_run: ["Web Applications", "Databases", "Analytics"],
  what_is_the_average_cpu_and_memory_usage_of_your_workloads: "Medium (50-70%)",
  do_you_require_high_availability_or_disaster_recovery_for_critical_applications: "Yes, 99.9% uptime required",
  
  // Section 3: Cloud Provider & Deployment Preferences  
  which_cloud_provider_s_are_you_considering: "AWS",
  do_you_plan_to_use_reserved_instances_spot_instances_or_pay_as_you_go_pricing_models: "Reserved instances for predictable workloads",
  which_cloud_environments_are_you_planning_to_deploy: ["Production", "Staging", "Development"],
  do_you_have_any_specific_compliance_or_regulatory_requirements_that_your_cloud_migration_needs_to_meet: "GDPR, SOC2",
  
  // Section 4: Security, Compliance & Migration Strategy
  what_migration_strategy_do_you_prefer: ["Lift and shift", "Re-platforming"],
  do_you_need_auto_scaling_capabilities_for_cost_optimization: "Yes, based on demand patterns",
  how_often_do_you_plan_to_review_and_optimize_your_cloud_expenses: "Monthly",
  
  // Cost breakdown factors
  costFactors: {
    serverCount: "25000",
    dataCapacity: "15000", 
    highAvailability: "10000",
    environments: "8000",
    compliance: "5000",
    migrationStrategy: "7000",
    autoScaling: "3000"
  },
  
  // Tracking and metadata
  consent: true,
  secondary_source: "Cloud Migration Cost Calculator",
  url: "https://example.com/cloud-migration-calculator",
  utm_campaign: "cloud-migration-2024",
  utm_source: "website",
  utm_medium: "organic",
  referrer: "https://google.com",
  ip_address: "***********",
  ga_4_userid: "GA1.1.123456789.1234567890",
  clarity: "https://clarity.microsoft.com/session/abc123"
};

// Create a mock Lambda event
const mockEvent = {
  body: JSON.stringify(testFormData),
  headers: {
    'Content-Type': 'application/json',
    'x-api-key': 'test-api-key'
  },
  requestContext: {
    requestId: 'test-request-id'
  }
};

// Test function
async function testCloudMigrationHandler() {
  console.log('🧪 Testing Cloud Migration Cost Calculator Handler...\n');
  
  try {
    console.log('📤 Sending test form data:');
    console.log(JSON.stringify(testFormData, null, 2));
    console.log('\n' + '='.repeat(50) + '\n');
    
    const result = await handler(mockEvent);
    
    console.log('📥 Handler Response:');
    console.log(`Status Code: ${result.statusCode}`);
    console.log(`Headers:`, result.headers);
    console.log(`Body:`, JSON.parse(result.body));
    
    if (result.statusCode === 200) {
      console.log('\n✅ Test PASSED: Handler returned success response');
    } else {
      console.log('\n❌ Test FAILED: Handler returned error response');
    }
    
  } catch (error) {
    console.error('\n💥 Test ERROR:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
console.log('Cloud Migration Cost Calculator Handler Test');
console.log('=' .repeat(50));
testCloudMigrationHandler();
